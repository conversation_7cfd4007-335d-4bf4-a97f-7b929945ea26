<template>
  <div class="sidebar-nav">
    <div class="sidebar-content">
      <!-- 导航菜单 -->
      <div class="nav-menu">
        <div
          v-for="item in menuItems"
          :key="item.id"
          class="nav-item"
          :class="{ active: item.active }"
          @click="handleMenuClick(item)"
        >
          <div class="nav-item-content">
            <!-- 图标 -->
            <div v-if="item.icon" class="nav-icon">
              <el-icon :size="18">
                <component :is="item.icon" />
              </el-icon>
            </div>

            <!-- 标题 -->
            <span class="nav-title">{{ item.title }}</span>

            <!-- 下拉箭头 -->
            <div class="nav-arrow">
              <el-icon :size="14" class="arrow-icon" :class="{ expanded: item.expanded }">
                <ArrowRight />
              </el-icon>
            </div>
          </div>

          <!-- 子菜单（暂时空实现） -->
          <div v-if="item.children && item.expanded" class="sub-menu">
            <div
              v-for="child in item.children"
              :key="child.id"
              class="sub-nav-item"
              :class="{ active: child.active }"
              @click.stop="handleSubMenuClick(child)"
            >
              <span class="sub-nav-title">{{ child.title }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  House,
  DataAnalysis,
  Setting,
  User,
  Document,
  Monitor,
  ArrowRight
} from '@element-plus/icons-vue'

// 菜单项接口定义
interface MenuItem {
  id: string
  title: string
  icon?: any
  active?: boolean
  expanded?: boolean
  children?: MenuItem[]
}

// 菜单数据
const menuItems = ref<MenuItem[]>([
  {
    id: 'dashboard',
    title: '首页',
    icon: House,
    active: true,
    expanded: false,
    children: [
      { id: 'overview', title: '概览', active: false },
      { id: 'statistics', title: '统计', active: false }
    ]
  },
  {
    id: 'data-analysis',
    title: '数据分析',
    icon: DataAnalysis,
    active: false,
    expanded: false,
    children: [
      { id: 'reports', title: '报表管理', active: false },
      { id: 'charts', title: '图表分析', active: false }
    ]
  },
  {
    id: 'system-management',
    title: '系统管理',
    icon: Setting,
    active: false,
    expanded: false,
    children: [
      { id: 'users', title: '用户管理', active: false },
      { id: 'roles', title: '角色管理', active: false },
      { id: 'permissions', title: '权限管理', active: false }
    ]
  },
  {
    id: 'user-center',
    title: '用户中心',
    icon: User,
    active: false,
    expanded: false,
    children: [
      { id: 'profile', title: '个人信息', active: false },
      { id: 'settings', title: '个人设置', active: false }
    ]
  },
  {
    id: 'documents',
    title: '文档管理',
    icon: Document,
    active: false,
    expanded: false,
    children: [
      { id: 'files', title: '文件管理', active: false },
      { id: 'templates', title: '模板管理', active: false }
    ]
  },
  {
    id: 'monitoring',
    title: '系统监控',
    icon: Monitor,
    active: false,
    expanded: false,
    children: [
      { id: 'performance', title: '性能监控', active: false },
      { id: 'logs', title: '日志管理', active: false }
    ]
  }
])

// 处理菜单点击
const handleMenuClick = (item: MenuItem) => {
  // 切换展开状态
  item.expanded = !item.expanded

  // 设置当前激活项
  menuItems.value.forEach(menu => {
    menu.active = menu.id === item.id
    if (menu.children) {
      menu.children.forEach(child => {
        child.active = false
      })
    }
  })

  console.log('菜单点击:', item.title)
}

// 处理子菜单点击
const handleSubMenuClick = (child: MenuItem) => {
  // 设置子菜单激活状态
  menuItems.value.forEach(menu => {
    menu.active = false
    if (menu.children) {
      menu.children.forEach(subItem => {
        subItem.active = subItem.id === child.id
      })
    }
  })

  console.log('子菜单点击:', child.title)
}
</script>

<style scoped>
.sidebar-nav {
  width: 250px;
  background: #ffffff;
  border-right: 1px solid #e6e8eb;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
  overflow-y: auto;
  overflow-x: hidden;
}

.sidebar-content {
  padding: 20px 0;
}

.nav-menu {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.nav-item {
  margin: 0 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.nav-item:hover {
  background: #f8f9fa;
}

.nav-item.active {
  background: #e3f2fd;
  border-left: 3px solid #1976d2;
}

.nav-item-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 12px;
}

.nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: #666;
  transition: color 0.3s ease;
}

.nav-item.active .nav-icon {
  color: #1976d2;
}

.nav-title {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  transition: color 0.3s ease;
}

.nav-item.active .nav-title {
  color: #1976d2;
  font-weight: 600;
}

.nav-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.arrow-icon {
  color: #999;
  transition: all 0.3s ease;
}

.arrow-icon.expanded {
  transform: rotate(90deg);
  color: #1976d2;
}

.sub-menu {
  margin-left: 20px;
  margin-right: 8px;
  border-left: 2px solid #f0f0f0;
  animation: slideDown 0.3s ease;
}

.sub-nav-item {
  padding: 8px 16px;
  margin: 2px 0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sub-nav-item:hover {
  background: #f5f5f5;
}

.sub-nav-item.active {
  background: #e8f4fd;
  border-left: 2px solid #1976d2;
}

.sub-nav-title {
  font-size: 13px;
  color: #666;
  transition: color 0.3s ease;
}

.sub-nav-item.active .sub-nav-title {
  color: #1976d2;
  font-weight: 500;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 200px;
  }
}
</style>
