<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h2 class="page-title">控制台</h2>
      <p class="page-description">系统概览和关键指标监控</p>
    </div>
    
    <div class="dashboard-content">
      <!-- 统计卡片 -->
      <div class="stats-grid">
        <el-card
          v-for="stat in statsData"
          :key="stat.id"
          class="stat-card"
          shadow="hover"
        >
          <div class="stat-content">
            <div class="stat-icon" :style="{ backgroundColor: stat.color + '20' }">
              <el-icon :size="24" :color="stat.color">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 图表区域 -->
      <div class="charts-section">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span class="card-title">数据趋势</span>
              <el-button type="primary" size="small">查看详情</el-button>
            </div>
          </template>
          <div class="chart-placeholder">
            <el-icon :size="48" color="#ccc">
              <DataAnalysis />
            </el-icon>
            <p>图表区域 - 可集成 ECharts 等图表库</p>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  User,
  Document,
  Monitor,
  DataAnalysis
} from '@element-plus/icons-vue'

// 统计数据
interface StatData {
  id: string
  label: string
  value: string
  icon: any
  color: string
}

const statsData = ref<StatData[]>([
  {
    id: 'users',
    label: '总用户数',
    value: '1,234',
    icon: User,
    color: '#1976d2'
  },
  {
    id: 'documents',
    label: '文档数量',
    value: '856',
    icon: Document,
    color: '#388e3c'
  },
  {
    id: 'systems',
    label: '在线系统',
    value: '12',
    icon: Monitor,
    color: '#f57c00'
  },
  {
    id: 'analytics',
    label: '今日访问',
    value: '2,468',
    icon: DataAnalysis,
    color: '#7b1fa2'
  }
])
</script>

<style scoped>
.dashboard {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.dashboard-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.dashboard-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  border-radius: 12px;
  border: 1px solid #e6e8eb;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  flex-shrink: 0;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.charts-section {
  flex: 1;
}

.chart-card {
  height: 400px;
  border-radius: 12px;
  border: 1px solid #e6e8eb;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.chart-placeholder {
  height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  gap: 16px;
}

.chart-placeholder p {
  margin: 0;
  font-size: 14px;
}

/* Element Plus 样式覆盖 */
:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}
</style>
